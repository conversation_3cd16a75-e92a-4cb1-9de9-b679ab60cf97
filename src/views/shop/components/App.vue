<template>
  <div>
    <el-dialog
      class="card"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      v-model="dialogVisible"
      width="30%"
      :align-center="true"
      title="自研应用管理"
    >
      <el-form
        class="form-box"
        label-width="100px"
        label-suffix=":"
        :model="dialogProps.raw.form"
        :rules="dialogProps.raw.rules"
        ref="formRef"
      >
        <el-form-item label="应用名称" prop="appName">
          <el-input v-model="dialogProps.raw.form.appName" placeholder="请输入应用名称" :clearable="true" />
        </el-form-item>
        <el-form-item label="appKey" prop="appKey">
          <el-input
            type="password"
            show-password
            v-model="dialogProps.raw.form.appKey"
            placeholder="请输入appKey"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="appSecret" prop="appSecret">
          <el-input
            type="password"
            show-password
            v-model="dialogProps.raw.form.appSecret"
            placeholder="请输入appSecret"
            :clearable="true"
          />
        </el-form-item>
        <el-form-item label="回调地址" prop="redirectUri">
          <el-input v-model="dialogProps.raw.form.redirectUri" placeholder="请输入回调地址" :clearable="true" />
          <el-text class="text-tips" style="padding-top: 10px">回调地址请填写:{{ dialogProps.raw.form.redirectUri }}</el-text>
        </el-form-item>
        <el-form-item label="绑定账户" prop="pin">
          <div>
            <el-button @click="handleGetQR">{{ dialogProps.raw.form.pin ? "重新绑定" : "点击绑定" }}</el-button>
          </div>
          <div>
            <el-text v-if="dialogProps.raw.form.pin" type="danger" class="ml10"> 已绑定: {{ dialogProps.raw.form.pin }} </el-text>
          </div>
        </el-form-item>
        <el-form-item>
          <el-text class="text-tips">绑定创建应用的账号，用于管理应用和授权状态</el-text>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
          <el-button @click="resetForm(formRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      v-model="showQRDialog"
      title="绑定账号"
      width="20%"
      :align-center="true"
      @close="handleClose"
      class="card"
    >
      <div style="width: 100%; text-align: center">
        <img width="150" :src="dialogProps.raw.form.qrImage" />
        <div v-if="dialogProps.raw.form.qrImage">
          <div v-if="dialogProps.raw.form.scanMsg === '手机客户端确认登录'" style="margin-top: 30px; font-size: 12px; color: red">
            {{ dialogProps.raw.form.scanMsg }}
          </div>
          <div v-else class="mt10 text-tips">请使用创建应用账号的手机京麦App扫码</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import { getShopLoginQR, getLoginStatus, updateOrCreateApp } from "@/views/shop/index";
const dialogVisible = ref(false);
const showQRDialog = ref(false);
interface Props {
  raw: { [key: string]: any };
  getTableList?: () => void;
}
const dialogProps = ref<Props>({
  raw: {}
});
const acceptParams = (raw: Props) => {
  dialogProps.value = raw;
  dialogVisible.value = true;
};

defineExpose({
  acceptParams
});

const formRef = ref<FormInstance>();
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      updateOrCreateApp({
        pin: dialogProps.value.raw.form.pin,
        appName: dialogProps.value.raw.form.appName,
        appKey: dialogProps.value.raw.form.appKey,
        appSecret: dialogProps.value.raw.form.appSecret,
        redirectUri: dialogProps.value.raw.form.redirectUri
      }).then(() => {
        ElMessage.success("操作成功");
        dialogVisible.value = false;
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

const handleGetQR = () => {
  // 获取二维码
  dialogProps.value.raw.form.pin = "";
  getShopLoginQR({ key: "appConfig" }).then(res => {
    dialogProps.value.raw.form.qrImage = res.data.image;
    dialogProps.value.raw.form.qrKey = res.data.key;
    showQRDialog.value = true;
    handleCheckLogin();
  });
};

// 定义定时器 ID
let intervalId: NodeJS.Timeout;
const handleCheckLogin = () => {
  intervalId = setInterval(() => {
    getLoginStatus({ key: dialogProps.value.raw.form.qrKey, from: "appConfig" }).then(res => {
      dialogProps.value.raw.form.scanMsg = res.data.msg;
      if (res.data.status) {
        dialogProps.value.raw.form.pin = res.data.pin;
        handleClose();
      } else {
        if (res.data.qrCodeStateType === "CANCELAUTH") {
          ElMessage.error("二维码已取消授权");
          handleClose();
        }
      }
    });
  }, 2000);
};

const handleClose = () => {
  clearInterval(intervalId);
  showQRDialog.value = false;
  dialogProps.value.raw.form.qrImage = "";
  dialogProps.value.raw.form.qrKey = "";
  dialogProps.value.raw.form.scanMsg = "";
  // 停止定时器
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
