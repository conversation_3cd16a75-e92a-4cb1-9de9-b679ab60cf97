# ApplicationForm 组件使用说明

## 概述

`ApplicationForm.vue` 是一个用于自研应用配置的对话框组件，按照 `Login.vue` 的设计风格创建，提供了完整的表单验证和提交功能。

## 功能特性

- 🎨 **统一设计风格**：完全按照 Login.vue 的设计风格和样式规范
- 📝 **完整表单验证**：包含所有必要字段的验证规则
- 🔒 **密码安全输入**：AppSecret 字段支持密码模式显示
- 💾 **自动保存**：表单提交后自动调用 `/application/save` 接口
- 🔄 **列表刷新**：保存成功后自动刷新父组件列表
- ✨ **用户体验**：加载状态、成功提示、错误处理

## 表单字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | ✅ | 应用名称，2-50个字符 |
| appKey | string | ✅ | 应用Key，最少10个字符 |
| appSecret | string | ✅ | 应用密钥，最少10个字符，密码输入 |
| redirectUri | string | ✅ | 回调地址，必须是有效的URL |
| pin | string | ✅ | PIN码，4-20个字符 |

## 使用方法

### 1. 导入组件

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button type="primary" @click="handleOpenForm">
      新增自研应用
    </el-button>
    
    <!-- 应用表单组件 -->
    <ApplicationForm ref="applicationFormRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ApplicationForm from "./ApplicationForm.vue";

const applicationFormRef = ref<InstanceType<typeof ApplicationForm>>();
</script>
```

### 2. 新增模式

```typescript
const handleAdd = () => {
  applicationFormRef.value?.acceptParams({
    getTableList: () => {
      // 刷新列表的回调函数
      console.log("刷新应用列表");
    }
  });
};
```

### 3. 编辑模式

```typescript
const handleEdit = (rowData: any) => {
  applicationFormRef.value?.acceptParams({
    row: {
      name: rowData.name,
      appKey: rowData.appKey,
      appSecret: rowData.appSecret,
      redirectUri: rowData.redirectUri,
      pin: rowData.pin
    },
    getTableList: () => {
      // 刷新列表的回调函数
      refreshTableList();
    }
  });
};
```

## API 接口

组件会调用以下接口：

```typescript
// POST /application/save
{
  name: string,        // 应用名称
  appKey: string,      // 应用Key
  appSecret: string,   // 应用密钥
  redirectUri: string, // 回调地址
  pin: string         // PIN码
}
```

## 样式特性

- 📱 **响应式设计**：对话框宽度 480px，适配不同屏幕
- 🎯 **居中显示**：对话框居中显示，支持拖拽
- 🎨 **统一风格**：与 Login.vue 保持一致的视觉风格
- 💡 **用户提示**：表单字段包含友好的提示信息
- 🔄 **加载状态**：提交时显示加载状态和禁用按钮

## 验证规则

- **应用名称**：必填，2-50个字符
- **AppKey**：必填，最少10个字符
- **AppSecret**：必填，最少10个字符
- **回调地址**：必填，必须是有效的HTTP/HTTPS URL
- **PIN码**：必填，4-20个字符

## 完整示例

参考 `ApplicationFormExample.vue` 文件查看完整的使用示例。

## 注意事项

1. 确保已在 `src/views/shop/index.ts` 中添加了 `saveApplication` API 方法
2. 组件使用 `ref` 方式调用，需要通过 `acceptParams` 方法传递参数
3. 表单验证失败时会阻止提交，成功时会显示成功消息并关闭对话框
4. 支持编辑模式，传入 `row` 参数即可预填充表单数据
