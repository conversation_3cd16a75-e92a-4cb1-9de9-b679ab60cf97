<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="监控Cookie状态"
      width="600px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div v-loading="loading" class="monitor-container">
        <div v-if="monitorData" class="monitor-content">
          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">任务状态</div>
              <div class="status-value">
                <el-tag :type="monitorData.isRunning ? 'success' : 'danger'">
                  {{ monitorData.taskStatus === "RUNNING" ? "运行中" : "已停止" }}
                </el-tag>
              </div>
            </div>

            <div class="status-item">
              <div class="status-label">运行时间</div>
              <div class="status-value">{{ monitorData.runningTimeFormatted }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">总店铺数</div>
              <div class="status-value">{{ monitorData.totalShopCount }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">已登录店铺</div>
              <div class="status-value">{{ monitorData.loginedShopCount }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">检查次数</div>
              <div class="status-value">{{ monitorData.checkCount }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">层级变更次数</div>
              <div class="status-value">{{ monitorData.levelChangedCount }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">最后检查时间</div>
              <div class="status-value">{{ formatTime(monitorData.lastCheckTime) }}</div>
            </div>

            <div class="status-item">
              <div class="status-label">暂停状态</div>
              <div class="status-value">
                <el-tag :type="monitorData.isPaused ? 'warning' : 'success'">
                  {{ monitorData.isPaused ? "已暂停" : "正常" }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无监控数据" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="refreshData" :loading="loading"> 刷新数据 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { getShopLevelTaskStatus, type Shop } from "@/views/shop/index";

const dialogVisible = ref(false);
const loading = ref(false);
const monitorData = ref<Shop.TaskStatus | null>(null);

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString();
};

// 获取监控数据
const fetchMonitorData = async () => {
  try {
    loading.value = true;
    const response = await getShopLevelTaskStatus();
    monitorData.value = response.data;
  } catch (error) {
    console.error("获取监控状态失败:", error);
    ElMessage.error("获取监控状态失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  fetchMonitorData();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  monitorData.value = null;
};

// 接受参数并打开对话框
const acceptParams = () => {
  dialogVisible.value = true;
  fetchMonitorData();
};

// 暴露方法
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.monitor-container {
  min-height: 200px;
}
.monitor-content {
  padding: 20px 0;
}
.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}
.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  border-radius: 8px;
}
.status-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}
.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}
</style>
