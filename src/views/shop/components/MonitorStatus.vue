<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="监控Cookie状态"
      width="480px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="form-container">
        <el-form label-width="120px" label-suffix=":">
          <el-form-item label="任务状态">
            <el-tag :type="monitorData?.isRunning ? 'success' : 'danger'">
              {{ monitorData?.taskStatus === "RUNNING" ? "运行中" : "已停止" }}
            </el-tag>
          </el-form-item>

          <el-form-item label="运行时间">
            <span>{{ monitorData?.runningTimeFormatted || "--" }}</span>
          </el-form-item>

          <el-form-item label="总店铺数">
            <span>{{ monitorData?.totalShopCount || 0 }}</span>
          </el-form-item>

          <el-form-item label="已登录店铺">
            <span>{{ monitorData?.loginedShopCount || 0 }}</span>
          </el-form-item>

          <el-form-item label="检查次数">
            <span>{{ monitorData?.checkCount || 0 }}</span>
          </el-form-item>

          <el-form-item label="层级变更次数">
            <span>{{ monitorData?.levelChangedCount || 0 }}</span>
          </el-form-item>

          <el-form-item label="最后检查时间">
            <span>{{ formatTime(monitorData?.lastCheckTime) }}</span>
          </el-form-item>

          <el-form-item label="暂停状态">
            <el-tag :type="monitorData?.isPaused ? 'warning' : 'success'">
              {{ monitorData?.isPaused ? "已暂停" : "正常" }}
            </el-tag>
          </el-form-item>

          <el-form-item label="停止状态">
            <el-tag :type="monitorData?.isStopped ? 'danger' : 'success'">
              {{ monitorData?.isStopped ? "已停止" : "正常" }}
            </el-tag>
          </el-form-item>

          <el-form-item label="运行时长(毫秒)">
            <span>{{ monitorData?.runningTimeMs || 0 }}</span>
          </el-form-item>

          <el-form-item label="响应码">
            <el-tag :type="monitorData?.code === 200 ? 'success' : 'danger'">
              {{ monitorData?.code || "--" }}
            </el-tag>
          </el-form-item>

          <el-form-item label="请求成功">
            <el-tag :type="monitorData?.success ? 'success' : 'danger'">
              {{ monitorData?.success ? "成功" : "失败" }}
            </el-tag>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose" :disabled="loading">关闭</el-button>
          <el-button type="primary" @click="refreshData" :loading="loading">
            {{ loading ? "刷新中..." : "刷新数据" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { getShopLevelTaskStatus, type Shop } from "@/views/shop/index";

const dialogVisible = ref(false);
const loading = ref(false);
const monitorData = ref<Shop.TaskStatus | null>(null);

// 格式化时间
const formatTime = (timestamp?: number) => {
  if (!timestamp) return "--";
  return new Date(timestamp).toLocaleString();
};

// 获取监控数据
const fetchMonitorData = async () => {
  try {
    loading.value = true;
    const response = await getShopLevelTaskStatus();
    monitorData.value = response.data;
  } catch (error) {
    console.error("获取监控状态失败:", error);
    ElMessage.error("获取监控状态失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  fetchMonitorData();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  monitorData.value = null;
};

// 接受参数并打开对话框
const acceptParams = () => {
  dialogVisible.value = true;
  fetchMonitorData();
};

// 暴露方法
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px 0;
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}

// 表单样式
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  .el-input__inner {
    border-radius: 6px;
  }
}
</style>
