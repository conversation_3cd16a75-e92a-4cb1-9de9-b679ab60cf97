<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="自研应用配置"
      width="480px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="form-container">
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-suffix=":">
          <el-form-item label="应用名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入应用名称" :clearable="true" />
          </el-form-item>

          <el-form-item label="AppKey" prop="appKey">
            <el-input v-model="formData.appKey" placeholder="请输入AppKey" :clearable="true" />
          </el-form-item>

          <el-form-item label="AppSecret" prop="appSecret">
            <el-input
              type="password"
              show-password
              v-model="formData.appSecret"
              placeholder="请输入AppSecret"
              :clearable="true"
            />
          </el-form-item>

          <el-form-item label="回调地址" prop="redirectUri">
            <el-input v-model="formData.redirectUri" placeholder="请输入回调地址" :clearable="true" />
            <div class="form-tips">
              <span>请填写完整的回调地址，如：https://example.com/callback</span>
            </div>
          </el-form-item>

          <el-form-item label="PIN码" prop="pin">
            <div class="pin-input-section">
              <el-input v-model="formData.pin" placeholder="请扫码获取PIN码" readonly :clearable="true" />
              <el-button type="primary" @click="handleGetPin" :loading="pinLoading" :disabled="!!formData.pin">
                {{ formData.pin ? "已获取" : "扫码获取" }}
              </el-button>
            </div>
            <div class="form-tips">
              <span>PIN码通过扫码自动获取，用于应用授权验证</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ loading ? "保存中..." : "保存" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- PIN码扫码对话框 -->
    <el-dialog
      v-model="pinDialogVisible"
      title="扫码获取PIN码"
      width="400px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handlePinDialogClose"
    >
      <!-- 倒计时显示 -->
      <div v-if="pinCountdown > 0" class="countdown-display">{{ pinCountdown }}s</div>
      <div class="pin-login-container">
        <div class="qr-code-section">
          <div class="qr-image-wrapper">
            <img v-if="qrCodeData.image" :src="qrCodeData.image" alt="二维码" class="qr-image" />
          </div>

          <div class="status-message">
            <div v-if="pinScanMsg === '手机客户端确认登录'" class="confirm-message">
              <el-icon class="status-icon"><Loading /></el-icon>
              <span>{{ pinScanMsg }}</span>
            </div>
            <div v-else-if="pinScanMsg === '二维码已过期'" class="expired-message">
              <span>{{ pinScanMsg }}</span>
            </div>
            <div v-else class="scan-tips">
              <span>请使用手机京麦App扫码获取PIN码</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handlePinDialogClose" :disabled="pinLoading">取消</el-button>
          <el-button type="primary" @click="refreshPinQRCode" :loading="pinLoading">
            {{ pinLoading ? "刷新中..." : "刷新" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Loading } from "@element-plus/icons-vue";
import { saveApplication, getShopLoginQR, getLoginStatus } from "@/views/shop/index";

const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

interface Props {
  row?: { [key: string]: any };
  getTableList?: () => void;
}

const dialogProps = ref<Props>({});

// 表单数据
const formData = reactive({
  name: "",
  appKey: "",
  appSecret: "",
  redirectUri: "",
  pin: ""
});

// PIN码扫码相关数据
const pinDialogVisible = ref(false);
const pinLoading = ref(false);
const pinCountdown = ref(0);
const pinScanMsg = ref("");
const qrCodeData = reactive({
  key: "",
  image: ""
});

// 定时器ID
let pinIntervalId: NodeJS.Timeout;
let pinTimeoutId: NodeJS.Timeout | undefined;
let pinCountdownId: NodeJS.Timeout;

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: "请输入应用名称", trigger: "blur" },
    { min: 2, max: 50, message: "应用名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  appKey: [
    { required: true, message: "请输入AppKey", trigger: "blur" },
    { min: 10, message: "AppKey长度不能少于10个字符", trigger: "blur" }
  ],
  appSecret: [
    { required: true, message: "请输入AppSecret", trigger: "blur" },
    { min: 10, message: "AppSecret长度不能少于10个字符", trigger: "blur" }
  ],
  redirectUri: [
    { required: true, message: "请输入回调地址", trigger: "blur" },
    {
      pattern: /^https?:\/\/.+/,
      message: "请输入有效的URL地址",
      trigger: "blur"
    }
  ],
  pin: [
    { required: true, message: "请输入PIN码", trigger: "blur" },
    { min: 4, max: 20, message: "PIN码长度在 4 到 20 个字符", trigger: "blur" }
  ]
};

// 接收参数
const acceptParams = (params: Props) => {
  dialogProps.value = params;

  // 如果有传入数据，则填充表单（编辑模式）
  if (params.row) {
    Object.assign(formData, {
      name: params.row.name || "",
      appKey: params.row.appKey || "",
      appSecret: params.row.appSecret || "",
      redirectUri: params.row.redirectUri || "",
      pin: params.row.pin || ""
    });
  } else {
    // 新增模式，重置表单
    resetForm();
  }

  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        await saveApplication({
          name: formData.name,
          appKey: formData.appKey,
          appSecret: formData.appSecret,
          redirectUri: formData.redirectUri,
          pin: formData.pin
        });

        ElMessage.success("保存成功");
        dialogVisible.value = false;

        // 刷新列表
        if (dialogProps.value.getTableList) {
          dialogProps.value.getTableList();
        }
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        loading.value = false;
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    appKey: "",
    appSecret: "",
    redirectUri: "",
    pin: ""
  });

  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 关闭对话框
// PIN码相关方法
const clearPinTimers = () => {
  if (pinIntervalId) {
    clearInterval(pinIntervalId);
  }
  if (pinTimeoutId) {
    clearTimeout(pinTimeoutId);
  }
  if (pinCountdownId) {
    clearInterval(pinCountdownId);
  }
};

const handleGetPin = async () => {
  try {
    pinLoading.value = true;
    const res = await getShopLoginQR();
    if (res && res.data) {
      qrCodeData.key = res.data.key;
      qrCodeData.image = res.data.image;
      pinDialogVisible.value = true;
      startPinPolling();
    } else {
      ElMessage.error("获取二维码失败");
    }
  } catch (error) {
    console.error("获取二维码失败:", error);
    ElMessage.error("获取二维码失败，请重试");
  } finally {
    pinLoading.value = false;
  }
};

const startPinPolling = () => {
  clearPinTimers();

  pinCountdown.value = 60;
  pinScanMsg.value = "";

  // 设置倒计时定时器
  pinCountdownId = setInterval(() => {
    pinCountdown.value--;
    if (pinCountdown.value <= 0) {
      clearPinTimers();
      ElMessage.warning("二维码已过期，请刷新后重试");
      pinScanMsg.value = "二维码已过期";
      pinCountdown.value = 0;
    }
  }, 1000);

  // 设置轮询定时器
  pinIntervalId = setInterval(() => {
    getLoginStatus({ key: qrCodeData.key, from: "bind" })
      .then(res => {
        pinScanMsg.value = res.data.msg;
        if (res.data.status) {
          // 扫码成功，获取PIN码
          formData.pin = res.data.pin;
          ElMessage.success("PIN码获取成功");
          handlePinDialogClose();
        } else {
          if (res.data.qrCodeStateType === "CANCELAUTH") {
            ElMessage.error("二维码已取消授权");
            handlePinDialogClose();
          }
        }
      })
      .catch(error => {
        console.error("获取登录状态失败:", error);
      });
  }, 2000);
};

const refreshPinQRCode = async () => {
  if (pinLoading.value) return;

  pinLoading.value = true;
  try {
    clearPinTimers();
    pinScanMsg.value = "";
    pinCountdown.value = 0;

    const res = await getShopLoginQR();
    if (res && res.data) {
      qrCodeData.key = res.data.key;
      qrCodeData.image = res.data.image;
      ElMessage.success("二维码已刷新");
      startPinPolling();
    } else {
      ElMessage.error("获取二维码失败");
    }
  } catch (error) {
    console.error("刷新二维码失败:", error);
    ElMessage.error("刷新二维码失败，请重试");
  } finally {
    pinLoading.value = false;
  }
};

const handlePinDialogClose = () => {
  clearPinTimers();
  pinScanMsg.value = "";
  pinCountdown.value = 0;
  pinDialogVisible.value = false;
};

const handleClose = () => {
  clearPinTimers();
  dialogVisible.value = false;
  resetForm();
};

// 暴露方法
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px 0;
}
.form-tips {
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;
  color: #909399;
}
.pin-input-section {
  display: flex;
  gap: 12px;
  align-items: center;
  .el-input {
    flex: 1;
  }
  .el-button {
    flex-shrink: 0;
  }
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}

// 全局dialog样式调整
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    position: relative;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 0 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
}

// 表单样式
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  .el-input__inner {
    border-radius: 6px;
  }
}

// PIN码扫码对话框样式
.pin-login-container {
  padding: 20px;
  text-align: center;
}
.qr-code-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}
.qr-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}
.qr-image {
  width: 150px;
  height: 150px;
  border-radius: 4px;
}
.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.confirm-message {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #e6a23c;
  .status-icon {
    font-size: 16px;
    animation: spin 1s linear infinite;
  }
}
.scan-tips {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 10px;
  font-size: 14px;
  color: #606266;
}
.expired-message {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #f56c6c;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 倒计时显示样式
.countdown-display {
  position: absolute;
  top: 20px;
  right: 50px;
  z-index: 1000;
  font-size: 14px;
  font-weight: 600;
  color: red;
}
</style>
