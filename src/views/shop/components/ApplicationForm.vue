<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="自研应用配置"
      width="480px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="form-container">
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-suffix=":">
          <el-form-item label="应用名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入应用名称" :clearable="true" />
          </el-form-item>

          <el-form-item label="AppKey" prop="appKey">
            <el-input v-model="formData.appKey" placeholder="请输入AppKey" :clearable="true" />
          </el-form-item>

          <el-form-item label="AppSecret" prop="appSecret">
            <el-input
              type="password"
              show-password
              v-model="formData.appSecret"
              placeholder="请输入AppSecret"
              :clearable="true"
            />
          </el-form-item>

          <el-form-item label="回调地址" prop="redirectUri">
            <el-input v-model="formData.redirectUri" placeholder="请输入回调地址" :clearable="true" />
            <div class="form-tips">
              <span>请填写完整的回调地址，如：https://example.com/callback</span>
            </div>
          </el-form-item>

          <el-form-item label="PIN码" prop="pin">
            <el-input v-model="formData.pin" placeholder="请输入PIN码" :clearable="true" />
            <div class="form-tips">
              <span>用于应用身份验证的PIN码</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ loading ? "保存中..." : "保存" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { saveApplication } from "@/views/shop/index";

const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

interface Props {
  row?: { [key: string]: any };
  getTableList?: () => void;
}

const dialogProps = ref<Props>({});

// 表单数据
const formData = reactive({
  name: "",
  appKey: "",
  appSecret: "",
  redirectUri: "",
  pin: ""
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: "请输入应用名称", trigger: "blur" },
    { min: 2, max: 50, message: "应用名称长度在 2 到 50 个字符", trigger: "blur" }
  ],
  appKey: [
    { required: true, message: "请输入AppKey", trigger: "blur" },
    { min: 10, message: "AppKey长度不能少于10个字符", trigger: "blur" }
  ],
  appSecret: [
    { required: true, message: "请输入AppSecret", trigger: "blur" },
    { min: 10, message: "AppSecret长度不能少于10个字符", trigger: "blur" }
  ],
  redirectUri: [
    { required: true, message: "请输入回调地址", trigger: "blur" },
    {
      pattern: /^https?:\/\/.+/,
      message: "请输入有效的URL地址",
      trigger: "blur"
    }
  ],
  pin: [
    { required: true, message: "请输入PIN码", trigger: "blur" },
    { min: 4, max: 20, message: "PIN码长度在 4 到 20 个字符", trigger: "blur" }
  ]
};

// 接收参数
const acceptParams = (params: Props) => {
  dialogProps.value = params;

  // 如果有传入数据，则填充表单（编辑模式）
  if (params.row) {
    Object.assign(formData, {
      name: params.row.name || "",
      appKey: params.row.appKey || "",
      appSecret: params.row.appSecret || "",
      redirectUri: params.row.redirectUri || "",
      pin: params.row.pin || ""
    });
  } else {
    // 新增模式，重置表单
    resetForm();
  }

  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        await saveApplication({
          name: formData.name,
          appKey: formData.appKey,
          appSecret: formData.appSecret,
          redirectUri: formData.redirectUri,
          pin: formData.pin
        });

        ElMessage.success("保存成功");
        dialogVisible.value = false;

        // 刷新列表
        if (dialogProps.value.getTableList) {
          dialogProps.value.getTableList();
        }
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        loading.value = false;
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    appKey: "",
    appSecret: "",
    redirectUri: "",
    pin: ""
  });

  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 暴露方法
defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.form-container {
  padding: 20px 0;
}
.form-tips {
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;
  color: #909399;
}
.dialog-footer {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  .el-button {
    min-width: 100px;
  }
}

// 全局dialog样式调整
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    position: relative;
    background: white;
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  .el-dialog__body {
    padding: 0 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
}

// 表单样式
:deep(.el-form) {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  .el-input__inner {
    border-radius: 6px;
  }
}
</style>
