<template>
  <div class="application-example">
    <div class="header-actions">
      <el-button type="primary" icon="Plus" @click="handleAdd">
        新增自研应用
      </el-button>
      <el-button type="success" icon="Edit" @click="handleEdit">
        编辑应用示例
      </el-button>
    </div>
    
    <!-- 应用表单对话框 -->
    <ApplicationForm ref="applicationFormRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import ApplicationForm from "./ApplicationForm.vue";

const applicationFormRef = ref<InstanceType<typeof ApplicationForm>>();

// 新增应用
const handleAdd = () => {
  applicationFormRef.value?.acceptParams({
    getTableList: () => {
      console.log("刷新应用列表");
      // 这里可以调用实际的列表刷新方法
    }
  });
};

// 编辑应用（示例数据）
const handleEdit = () => {
  const mockData = {
    name: "测试应用",
    appKey: "test_app_key_123456",
    appSecret: "test_app_secret_abcdef",
    redirectUri: "https://example.com/callback",
    pin: "test_pin_123"
  };
  
  applicationFormRef.value?.acceptParams({
    row: mockData,
    getTableList: () => {
      console.log("刷新应用列表");
      // 这里可以调用实际的列表刷新方法
    }
  });
};
</script>

<style scoped lang="scss">
.application-example {
  padding: 20px;
}
.header-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}
</style>
