<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="编辑分组"
      width="480px"
      :draggable="true"
      :align-center="true"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <div class="group-container">
        <el-form :model="form" ref="formRef" :rules="rules" label-position="top">
          <el-form-item label="当前分组名称">
            <el-input 
              v-model="form.currentName" 
              disabled 
              placeholder="当前分组名称"
            />
          </el-form-item>
          <el-form-item label="新分组名称" prop="newName">
            <el-input 
              v-model="form.newName" 
              placeholder="请输入新的分组名称" 
              clearable 
              maxlength="20"
              show-word-limit
              @keyup.enter="submitForm"
            />
          </el-form-item>
          <el-form-item>
            <el-text type="info">分组名称不能为空且不能与现有分组重复。</el-text>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="loading">
            {{ loading ? '保存中...' : '确认修改' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import http from '@/api'
import { type Group, groupList } from '@/views/main/interface/group'

const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const loading = ref(false)

interface Form {
  groupId: number
  currentName: string
  newName: string
}

const form = reactive<Form>({
  groupId: 0,
  currentName: '',
  newName: ''
})

// 表单验证规则
const rules = {
  newName: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 20, message: '分组名称长度在 1 到 20 个字符', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value) {
          // 检查是否与当前名称相同
          if (value === form.currentName) {
            callback(new Error('新名称不能与当前名称相同'))
            return
          }
          // 检查是否与其他分组重复
          const isExist = groupList.value.find((item) => item.name === value && item.id !== form.groupId)
          if (isExist) {
            callback(new Error('分组名称已存在'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 重置表单数据
const resetForm = () => {
  form.groupId = 0
  form.currentName = ''
  form.newName = ''
  formRef.value?.clearValidate()
}

// 接受参数并打开对话框
const acceptParams = (group: Group) => {
  resetForm()
  form.groupId = group.id
  form.currentName = group.name
  form.newName = group.name
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  loading.value = true

  try {
    const res = await http.put('/api/device/group/update', {
      groupId: form.groupId,
      name: form.newName
    })

    if (res.code === 200) {
      ElMessage.success('分组名称修改成功')
      
      // 更新本地数据
      const currentGroup = groupList.value.find((g) => g.id === form.groupId)
      if (currentGroup) {
        currentGroup.name = form.newName
      }
      
      dialogVisible.value = false
      resetForm()
    } else {
      ElMessage.error(res.msg || '修改分组名称失败')
    }
  } catch (error) {
    console.error('修改分组名称失败:', error)
    ElMessage.error('修改分组名称失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

defineExpose({
  acceptParams
})
</script>

<style lang="scss" scoped>
.group-container {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-shadow: none;
  transition: border-color 0.3s;

  &:hover {
    border-color: #c0c4cc;
  }

  &.is-focus {
    border-color: #409eff;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  background: #fafafa;
  border-radius: 0 0 8px 8px;
  margin: 0 -20px -20px -20px;

  .el-button {
    min-width: 100px;
  }
}

// 全局dialog样式调整
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;

  .el-dialog__header {
    background: white;
    padding: 20px 20px 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 0;
  }
}
</style>
