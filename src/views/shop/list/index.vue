<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getShopList" highlight-current-row :pagination="true">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button class="button" type="primary" icon="CirclePlus" @click="handelShopLogin">添加店铺</el-button>
        <el-button type="primary" icon="CirclePlus">自研应用</el-button>
        <el-button type="primary" icon="AlarmClock">监控Cookie状态</el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation>
        <el-button type="primary" plain>刷新</el-button>
        <!-- <el-button type="primary" @click="handleAddShop(scope.row)">编辑</el-button> -->
      </template>
    </ProTable>
    <Login ref="loginRef" />
  </div>
</template>
<script lang="tsx" setup name="shopList">
import { reactive, ref } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Shop, getShopList, getAccessToken, getShopLoginQR } from "@/views/shop/index";
import { ElMessage } from "element-plus";
import Login from "@/views/shop/components/Login.vue";
const loginRef = ref<InstanceType<typeof Login> | null>(null);

const proTable = ref<ProTableInstance>();
// ProTable 实例

const columns = reactive<ColumnProps<Shop.List>[]>([
  { type: "selection", fixed: "left" },
  { type: "index", label: "序号", width: 70 },
  {
    prop: "logined",
    label: "登录状态",
    render: scope => {
      if (scope.row.logined) {
        return <el-tag type="success">正常</el-tag>;
      } else {
        return <el-tag type="danger">失效</el-tag>;
      }
    }
  },
  { prop: "shopName", label: "店铺名称" },
  { prop: "shopType", label: "店铺类型" },
  { prop: "shopLevel", label: "店铺层级" },
  { prop: "id", label: "店铺ID" },
  { prop: "venderId", label: "商家ID" },
  {
    prop: "accessToken",
    label: "授权状态",
    render: scope => {
      // const { accessToken } = scope.row;
      // if (!accessToken) {
      //   return (
      //     <el-tag style={{ cursor: "pointer" }} type="info" onClick={() => handleGetAccessToken("fetch", scope.row)}>
      //       获取授权
      //     </el-tag>
      //   );
      // }

      const isExpired = scope.row.isExpired;
      const expireTime = scope.row.expireTime;
      // 如果未null则表示未获取授权
      if (isExpired === null || expireTime === null) {
        return (
          <el-tag style={{ cursor: "pointer" }} type="info" onClick={() => handleGetAccessToken("fetch", scope.row)}>
            获取授权
          </el-tag>
        );
      }

      const tagType = isExpired ? "danger" : "success";
      const tagText = isExpired ? "刷新授权" : "已授权";
      const tooltipContent = "到期时间：" + scope.row.expireTime;
      {
        /* <el-tag style={{ cursor: "pointer" }} type={tagType} v-copy={scope.row.accessToken}> */
      }

      return (
        <el-tooltip class="box-item" effect="dark" content={tooltipContent} placement="bottom">
          {isExpired ? (
            <el-tag style={{ cursor: "pointer" }} type={tagType} onClick={() => handleGetAccessToken("refresh", scope.row)}>
              {/* 刷新授权 */}
              {tagText}
            </el-tag>
          ) : (
            <el-tag style={{ cursor: "pointer" }} type={tagType}>
              {/* 授权正常 */}
              {tagText}
            </el-tag>
          )}
        </el-tooltip>
      );
    }
  },
  { prop: "updateTime", label: "更新时间" },
  { prop: "operation", label: "操作", fixed: "right" }
]);

const handleGetAccessToken = (action: string, row: Shop.List) => {
  getAccessToken({ shopId: row.id, action: action }).then(() => {
    ElMessage.success("操作成功");
    proTable.value?.getTableList();
  });
};

const handelShopLogin = () => {
  getShopLoginQR().then(res => {
    loginRef.value?.acceptParams({
      row: {
        key: res.data.key,
        image: res.data.image,
        getTableList: proTable.value?.getTableList
      }
    });
  });
};
</script>
<style scoped>
.desc-box {
  display: flex;
  padding: 10px;
  margin-bottom: 20px;
  background: linear-gradient(90deg, #fff8eb, #ffffff);
  border-radius: 12px;
}
.desc-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  font-size: 14px;
  line-height: 20px;
  color: #ff9702;
}
</style>
