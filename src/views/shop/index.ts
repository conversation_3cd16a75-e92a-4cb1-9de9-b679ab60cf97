import http from "@/api";
import { ResPage } from "@/api/interface";
// import { Result } from "@/api/interface";
// import { Result } from "@/api/interface/index";

export namespace Shop {
  export interface List {
    shopType: string;
    id: string;
    shopName: string;
    pin: string;
    logined: boolean;
    shopLevel: string;
    venderId: number;
    platformPoint: number;
    point: number;
    status: boolean;
    expireTime: string;
    isExpired: boolean;
    time: string;
  }
  export interface QR {
    key: string;
    image: string;
  }
  export interface LoginCheck {
    status: boolean;
    pin: string;
    sToken: string;
    msg: string;
    qrCodeStateType: string;
  }
  export interface App {
    pin: string;
    name: string;
    appKey: string;
    appSecret: string;
    redirectUri: string;
  }
  export interface ApplicationInfo {
    name: string;
    appKey: string;
    appSecret: string;
    redirectUri: string;
    pin: string;
    createTime: string;
    updateTime: string;
  }
}

export const getShopList = () => {
  return http.get<ResPage<Shop.List>>("/shop/list");
};

export const getShopLoginQR = () => {
  return http.get<Shop.QR>("/shop/qrcode");
};

export const getLoginStatus = (params: { from: string; key: string }) => {
  return http.post<Shop.LoginCheck>("/shop/qrcode/check", params, { loading: false });
};

export const updateShopInfo = (params: any) => {
  return http.put("/shop", params);
};

export const setShopAdmin = (params: any) => {
  return http.post("/shop/admin-account", params);
};

export const getAccessToken = (params: any) => {
  return http.post("/shop/access-token", params);
};

export const updateOrCreateApp = (params: any) => {
  return http.post("/shop/app", params);
};

export const getAppList = (params?: any) => {
  return http.get<Shop.App>("/shop/app", params);
};

export const saveApplication = (params: any) => {
  return http.post("/application/save", params);
};

export const getApplicationInfo = () => {
  return http.get<Shop.ApplicationInfo>("/application/info");
};
