import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/stores/helper/persist";

export const useUserStore = defineStore({
  id: "geeker-user",
  state: (): UserState => ({
    token: "",
    userInfo: { name: "" }
  }),
  getters: {},
  actions: {
    // Set Token
    setToken(token: string) {
      this.token = token;
    },
    // 设置用户名
    // Set setUserInfo
    setUserName(username: string) {
      this.userInfo.name = username;
    }
  },
  persist: piniaPersistConfig("geeker-user")
});
